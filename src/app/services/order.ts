import { Injectable } from '@angular/core';

export interface OrderConfig {
  customer_id: string;
  customer_name: string;
  facility_id: string;
  facility_name: string;
  authToken: string;
}

export interface OrderItem {
  sku: string;
  quantity: number;
  unit_price: number;
  sale_price: number;
}

export interface CreateOrderPayload {
  customer_id: string;
  customer_name: string;
  facility_id: string;
  facility_name: string;
  status: string;
  total_amount: number;
  items: OrderItem[];
}

@Injectable({
  providedIn: 'root',
})
export class OrderService {
  private defaultConfig: OrderConfig = {
    customer_id: 'CUST-001',
    customer_name: 'Test User',
    facility_id: 'FAC-001',
    facility_name: 'Test Facility',
    authToken: 'test-token',
  };

  constructor() {}

  setAuthToken(token: string): void {
    // Ensure token doesn't have quotes around it
    const cleanToken = token.replace(/^["']|["']$/g, '');
    localStorage.setItem('auth_token', cleanToken);
    this.defaultConfig.authToken = cleanToken;
    console.log('Token set:', cleanToken);
  }

  getAuthToken(): string | null {
    return localStorage.getItem('auth_token');
  }

  setCustomerInfo(customerId: string, customerName: string): void {
    this.defaultConfig.customer_id = customerId;
    this.defaultConfig.customer_name = customerName;
  }

  setFacilityInfo(facilityId: string, facilityName: string): void {
    this.defaultConfig.facility_id = facilityId;
    this.defaultConfig.facility_name = facilityName;
  }

  createOrderPayload(items: any[], totalAmount: number): CreateOrderPayload {
    const orderItems: OrderItem[] = items.map((item) => ({
      sku: item.sku || `SKU-${item.id}`,
      quantity: item.quantity,
      unit_price: item.price,
      sale_price: item.price,
    }));

    return {
      customer_id: this.defaultConfig.customer_id,
      customer_name: this.defaultConfig.customer_name,
      facility_id: this.defaultConfig.facility_id,
      facility_name: this.defaultConfig.facility_name,
      status: 'pending',
      total_amount: totalAmount,
      items: orderItems,
    };
  }

  getConfig(): OrderConfig {
    return { ...this.defaultConfig };
  }
}
