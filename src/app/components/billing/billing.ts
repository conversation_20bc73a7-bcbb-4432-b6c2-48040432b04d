import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  Output,
  OnChanges,
  SimpleChanges,
  ChangeDetectorRef,
  ViewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { IftaLabelModule } from 'primeng/iftalabel';
import { InputTextModule } from 'primeng/inputtext';
import { NoDataComponent } from '../no-data/no-data';
import { ProductComponent } from '../product/product';
import { TableModule } from 'primeng/table';
import { InputNumberModule } from 'primeng/inputnumber';
import { DividerModule } from 'primeng/divider';
import { ButtonModule } from 'primeng/button';
import { CommonService } from '../../services/common';
import { TypeSenseService } from '../../services/typesense';
import { OrderService } from '../../services/order';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { ProgressSpinnerModule } from 'primeng/progressspinner';

@Component({
  selector: 'app-billing',
  standalone: true,
  templateUrl: './billing.html',
  imports: [
    CommonModule,
    FormsModule,
    IftaLabelModule,
    InputTextModule,
    NoDataComponent,
    ProductComponent,
    TableModule,
    InputNumberModule,
    DividerModule,
    ButtonModule,
    AutoCompleteModule,
    ProgressSpinnerModule,
  ],
})
export class BillingComponent implements OnChanges {
  @Input() cartItems: any[] = [];
  @Input() quantityEdit = false;
  @Input() showActions = false;
  @Output() searchEvent = new EventEmitter<{
    searchText: string;
    event: any;
    keyCode: number;
  }>();
  searchText: string = '';
  selectedProduct: any = null;
  products: any[] = [];
  filteredProducts: any[] = [];
  selectedIndex: number = 0;


  // Checkout properties
  isProcessingCheckout = false;

  @ViewChild('searchInput') searchInput: any;
  constructor(
    private commonService: CommonService,
    private typesenseService: TypeSenseService,
    private orderService: OrderService,
    private cdr: ChangeDetectorRef,
  ) {}
  ngOnChanges(_changes: SimpleChanges): void {
    console.log(this.cartItems);
  }

  addToCart(product: any) {
    const cartItem = this.cartItems?.find(
      (item: any) => item.id === product.id,
    );
    if (cartItem) {
      cartItem.quantity += 1;
    } else {
      this.cartItems?.push({ ...product, quantity: 1 });
      this.cdr.detectChanges();
    }
    // Clear the search and selection after adding to cart
    this.products = [];
    this.selectedProduct = null;
    this.filteredProducts = [];
  }

  onSearchChange(event: any) {
    const query = event.query;
    if (query && query.length > 2) {
      this.getsearchProducts(query);
    } else {
      this.filteredProducts = [];
    }
  }
  getsearchProducts(query: string) {
    this.typesenseService.searchProducts(query).then((result) => {
      this.filteredProducts = result.products || [];
    });
  }
  removeFromCart(product: any) {
    const index = this.cartItems.findIndex((item) => item.id === product.id);
    if (index > -1) {
      this.cartItems.splice(index, 1);
    }
  }
  updateQuantity(event: any, product: any) {
    const newQuantity = event.value;

    if (newQuantity < 1) {
      // Remove item from cart if quantity is less than 1
      this.removeFromCart(product);
    } else {
      product.quantity = newQuantity;
    }
  }
  getSubTotal() {
    return this.cartItems.reduce(
      (total, item) => total + item.price * item.quantity,
      0,
    );
  }
  getDiscount() {
    return (
      this.cartItems.reduce(
        (total, item) => total + item.price * item.quantity,
        0,
      ) * 0.1
    );
  }
  getGrandTotal() {
    return (
      this.cartItems.reduce(
        (total, item) => total + item.price * item.quantity,
        0,
      ) - this.getDiscount()
    );
  }
  onSearch(event: any) {
    this.searchEvent.emit({
      searchText: event.target.value,
      event,
      keyCode: event.keyCode || 0,
    });
  }
  onClearSearch() {
    this.searchText = '';
  }
  clearCart() {
    this.cartItems = [];
  }

  checkout() {
    if (this.cartItems.length === 0) {
      this.commonService.toast({
        severity: 'warn',
        summary: 'Warning',
        detail: 'Cart is empty. Add items to checkout.',
      });
      return;
    }

    // Clear any existing token and set a fresh one for testing
    localStorage.removeItem('auth_token');
    this.orderService.setAuthToken('test-token');

    this.isProcessingCheckout = true;

    // Use OrderService to create the payload
    const totalAmount = this.getGrandTotal();
    const orderData = this.orderService.createOrderPayload(this.cartItems, totalAmount);

    console.log('Creating order with data:', orderData);

    this.commonService.post('create_order', orderData).subscribe({
      next: (response) => {
        console.log('Order created successfully:', response);
        this.commonService.toast({
          severity: 'success',
          summary: 'Success',
          detail: `Order created successfully! Total: ₹${orderData.total_amount.toFixed(2)}`
        });
        this.clearCart();
        this.isProcessingCheckout = false;
      },
      error: (error) => {
        console.error('Order creation error:', error);
        let errorMessage = 'Order creation failed. Please try again.';

        if (error.status === 401) {
          errorMessage = 'Authentication failed. Please check your token.';
        } else if (error.status === 400) {
          errorMessage = 'Invalid order data. Please check the items.';
        }

        this.commonService.toast({
          severity: 'error',
          summary: 'Error',
          detail: errorMessage
        });
        this.isProcessingCheckout = false;
      }
    });
  }

  // Method to update token if needed
  setAuthToken(token: string): void {
    this.orderService.setAuthToken(token);
  }

  // Method to update customer info if needed
  setCustomerInfo(customerId: string, customerName: string): void {
    this.orderService.setCustomerInfo(customerId, customerName);
  }

  // Method to update facility info if needed
  setFacilityInfo(facilityId: string, facilityName: string): void {
    this.orderService.setFacilityInfo(facilityId, facilityName);
  }

  // Quick setup method for testing - call this from browser console
  setupForTesting(token: string, customerId?: string, customerName?: string): void {
    this.setAuthToken(token);
    if (customerId && customerName) {
      this.setCustomerInfo(customerId, customerName);
    }
    console.log('Setup complete! You can now create orders.');
    console.log('Current config:', this.orderService.getConfig());
  }

  // Method to test with working backend token
  useWorkingToken(): void {
    // Clear any existing token first
    localStorage.removeItem('auth_token');
    // Set the token that works with your other backend
    this.orderService.setAuthToken('test-token');
    console.log('Using working token: test-token');
  }

  // Method to test backend connectivity
  testBackendConnection(): void {
    console.log('Testing backend connection...');
    console.log('Base URL:', this.commonService.baseUrl);
    console.log('Full URL:', `${this.commonService.baseUrl}create_order`);

    // Test with a simple GET request first (if available)
    this.commonService.get('health').subscribe({
      next: (response) => {
        console.log('Backend is reachable:', response);
      },
      error: (error) => {
        console.log('Backend connection test failed:', error);
        console.log('This might be normal if there\'s no health endpoint');
      }
    });
  }
}
