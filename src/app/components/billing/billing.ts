import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  Output,
  OnChanges,
  SimpleChanges,
  ChangeDetectorRef,
  ViewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { IftaLabelModule } from 'primeng/iftalabel';
import { InputTextModule } from 'primeng/inputtext';
import { NoDataComponent } from '../no-data/no-data';
import { ProductComponent } from '../product/product';
import { TableModule } from 'primeng/table';
import { InputNumberModule } from 'primeng/inputnumber';
import { DividerModule } from 'primeng/divider';
import { ButtonModule } from 'primeng/button';
import { CommonService } from '../../services/common';
import { TypeSenseService } from '../../services/typesense';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { ProgressSpinnerModule } from 'primeng/progressspinner';

@Component({
  selector: 'app-billing',
  standalone: true,
  templateUrl: './billing.html',
  imports: [
    CommonModule,
    FormsModule,
    IftaLabelModule,
    InputTextModule,
    NoDataComponent,
    ProductComponent,
    TableModule,
    InputNumberModule,
    DividerModule,
    ButtonModule,
    AutoCompleteModule,
    ProgressSpinnerModule,
  ],
})
export class BillingComponent implements OnChanges {
  @Input() cartItems: any[] = [];
  @Input() quantityEdit = false;
  @Input() showActions = false;
  @Output() searchEvent = new EventEmitter<{
    searchText: string;
    event: any;
    keyCode: number;
  }>();
  searchText: string = '';
  selectedProduct: any = null;
  products: any[] = [];
  filteredProducts: any[] = [];
  selectedIndex: number = 0;


  // Checkout properties
  isProcessingCheckout = false;

  @ViewChild('searchInput') searchInput: any;
  constructor(
    private commonService: CommonService,
    private typesenseService: TypeSenseService,
    private cdr: ChangeDetectorRef,
  ) {}
  ngOnChanges(_changes: SimpleChanges): void {
    console.log(this.cartItems);
  }

  addToCart(product: any) {
    const cartItem = this.cartItems?.find(
      (item: any) => item.id === product.id,
    );
    if (cartItem) {
      cartItem.quantity += 1;
    } else {
      this.cartItems?.push({ ...product, quantity: 1 });
      this.cdr.detectChanges();
    }
    // Clear the search and selection after adding to cart
    this.products = [];
    this.selectedProduct = null;
    this.filteredProducts = [];
  }

  onSearchChange(event: any) {
    const query = event.query;
    if (query && query.length > 2) {
      this.getsearchProducts(query);
    } else {
      this.filteredProducts = [];
    }
  }
  getsearchProducts(query: string) {
    this.typesenseService.searchProducts(query).then((result) => {
      this.filteredProducts = result.products || [];
    });
  }
  removeFromCart(product: any) {
    const index = this.cartItems.findIndex((item) => item.id === product.id);
    if (index > -1) {
      this.cartItems.splice(index, 1);
    }
  }
  updateQuantity(event: any, product: any) {
    const newQuantity = event.value;

    if (newQuantity < 1) {
      // Remove item from cart if quantity is less than 1
      this.removeFromCart(product);
    } else {
      product.quantity = newQuantity;
    }
  }
  getSubTotal() {
    return this.cartItems.reduce(
      (total, item) => total + item.price * item.quantity,
      0,
    );
  }
  getDiscount() {
    return (
      this.cartItems.reduce(
        (total, item) => total + item.price * item.quantity,
        0,
      ) * 0.1
    );
  }
  getGrandTotal() {
    return (
      this.cartItems.reduce(
        (total, item) => total + item.price * item.quantity,
        0,
      ) - this.getDiscount()
    );
  }
  onSearch(event: any) {
    this.searchEvent.emit({
      searchText: event.target.value,
      event,
      keyCode: event.keyCode || 0,
    });
  }
  onClearSearch() {
    this.searchText = '';
  }
  clearCart() {
    this.cartItems = [];
  }

  checkout() {
    if (this.cartItems.length === 0) {
      this.commonService.toast({
        severity: 'warn',
        summary: 'Warning',
        detail: 'Cart is empty. Add items to checkout.',
      });
      return;
    }

    // Clear any existing token and try different token formats
    localStorage.removeItem('auth_token');

    // Try the token format that works with your other backend
    // You can change this to match what works on port 3000
    localStorage.setItem('auth_token', 'test-token');

    console.log('=== CHECKOUT DEBUG INFO ===');
    console.log('Backend URL:', this.commonService.baseUrl);
    console.log('Token being used:', localStorage.getItem('auth_token'));
    console.log('Cart items:', this.cartItems);

    this.isProcessingCheckout = true;

    // Create simple order payload
    const totalAmount = this.getGrandTotal();
    const orderData = {
      customer_id: 'CUST-001',
      customer_name: 'Test User',
      facility_id: 'FAC-001',
      facility_name: 'Test Facility',
      status: 'pending',
      total_amount: totalAmount,
      items: this.cartItems.map(item => ({
        sku: item.sku || `SKU-${item.id}`,
        quantity: item.quantity,
        unit_price: item.price,
        sale_price: item.price
      }))
    };

    console.log('Creating order with data:', orderData);

    this.commonService.post('create_order', orderData).subscribe({
      next: (response) => {
        console.log('Order created successfully:', response);
        this.commonService.toast({
          severity: 'success',
          summary: 'Success',
          detail: `Order created successfully! Total: ₹${totalAmount.toFixed(2)}`
        });
        this.clearCart();
        this.isProcessingCheckout = false;
      },
      error: (error) => {
        console.error('Order creation error:', error);
        let errorMessage = 'Order creation failed. Please try again.';

        if (error.status === 401) {
          errorMessage = 'Authentication failed. Please check your token.';
        } else if (error.status === 400) {
          errorMessage = 'Invalid order data. Please check the items.';
        }

        this.commonService.toast({
          severity: 'error',
          summary: 'Error',
          detail: errorMessage
        });
        this.isProcessingCheckout = false;
      }
    });
  }

  // Method to update token if needed
  setAuthToken(token: string): void {
    localStorage.setItem('auth_token', token);
    console.log('Token set:', token);
  }

  // Method to test with working backend token
  useWorkingToken(): void {
    // Clear any existing token first
    localStorage.removeItem('auth_token');
    // Set the token that works with your other backend
    localStorage.setItem('auth_token', 'test-token');
    console.log('Using working token: test-token');
  }

  // Method to test backend connectivity
  testBackendConnection(): void {
    console.log('Testing backend connection...');
    console.log('Base URL:', this.commonService.baseUrl);
    console.log('Full URL:', `${this.commonService.baseUrl}create_order`);
    console.log('Current token:', localStorage.getItem('auth_token'));
  }

  // Method to test different token formats
  testDifferentTokens(): void {
    console.log('=== Testing Different Token Formats ===');

    const tokensToTry = [
      'test-token',
      'Bearer test-token',
      null
    ];

    tokensToTry.forEach((token, index) => {
      console.log(`\n--- Testing Token ${index + 1} ---`);
      console.log('Token:', token);

      if (token === null) {
        localStorage.removeItem('auth_token');
        console.log('Removed token from localStorage');
      } else {
        localStorage.setItem('auth_token', token);
        console.log('Set token in localStorage');
      }

      console.log('Current localStorage token:', localStorage.getItem('auth_token'));
    });

    // Reset to default
    localStorage.setItem('auth_token', 'test-token');
    console.log('\n=== Reset to default token: test-token ===');
  }

  // Method to compare with working backend
  compareWithWorkingBackend(): void {
    console.log('=== Backend Comparison ===');
    console.log('Current backend (not working):', this.commonService.baseUrl);
    console.log('Working backend should be: http://localhost:3000 -> backend on port 8000');
    console.log('');
    console.log('Questions to check:');
    console.log('1. Is the backend at ************:8000 running?');
    console.log('2. Does it have CORS enabled for localhost:3000?');
    console.log('3. Does it use the same authentication as your working backend?');
    console.log('4. Are the API endpoints the same?');
  }

  // Temporary method to test with working backend URL
  testWithWorkingBackend(): void {
    console.log('=== Testing with Working Backend ===');
    console.log('Original backend:', this.commonService.baseUrl);

    // Temporarily change the baseUrl to your working backend
    this.commonService.baseUrl = 'http://*************:8000/'; // Your working backend

    console.log('Switched to working backend:', this.commonService.baseUrl);
    console.log('Try checkout now, then call resetToOriginalBackend() to switch back');
  }

  // Method to reset back to original backend
  resetToOriginalBackend(): void {
    this.commonService.baseUrl = 'http://************:8000/';
    console.log('Reset to original backend:', this.commonService.baseUrl);
  }
}
