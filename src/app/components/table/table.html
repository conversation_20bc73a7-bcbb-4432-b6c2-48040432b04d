<div class="w-full mb-2">
    <p-iftalabel>
        <input class="w-full" pInputText type="text" (input)="table.filterGlobal($any($event.target).value, 'contains')" placeholder="Search keyword" />
        <label for="username">Search</label>
    </p-iftalabel>
</div>
<p-table [globalFilterFields]="globalFilterFields" (onHeaderCheckboxToggle)="onSelectAll($event, table)" (onRowUnselect)="onRowSelect($event, table)" (onRowSelect)="onRowSelect($event, table)" #table showGridlines [(selection)]="selectedRows" [selectionMode]="selectionMode" stripedRows [paginator]="true" [rows]="10" [rowsPerPageOptions]="[10, 20, 50, 100]" [value]="tableData" [dataKey]="dataKey">
  <ng-template #header let-index="index">
        <tr>
            <th style="width: 30px" *ngIf="selection">
              <div class="flex items-center gap-2"> 
                <p-tableHeaderCheckbox class="leading-none" />
              </div>
            </th>
            <th *ngFor="let col of tableColumns" style="width: 6rem">
              <div class="flex items-center">
                {{col.header}} 
              </div>
            </th>
        </tr>
    </ng-template>
    <ng-template #body let-product let-rowIndex="rowIndex">
        <tr [pSelectableRow]="product">
            <td *ngIf="selection">
                <div class="flex items-center gap-2">
                    <p-tableCheckbox class="leading-none" [value]="product"></p-tableCheckbox>
                </div>
            </td>
            <td *ngFor="let col of tableColumns" style="width: 6rem" class="leading-none">{{product[col.field]}}</td>
        </tr>
    </ng-template>
</p-table>