import { Component } from "@angular/core";
import { IonContent } from "@ionic/angular/standalone";
import { HeaderComponent } from "../../components/header/header";
import { TableComponent } from "../../components/table/table";
import { BillingComponent } from "../../components/billing/billing";
import { DataService } from "src/app/services/data";
@Component({
    selector: 'app-invoices',
    standalone: true,
    templateUrl: './invoices.html',
    imports: [IonContent, HeaderComponent, TableComponent, BillingComponent]
})
export class InvoicesComponent {
    invoices: any[] = [];
    invoicesColumns: any[] = [];
    cartItems: any[] = [];
    constructor(
        private data: DataService
    ) {
        this.invoicesColumns = [
            { field: 'id', header: 'ID' },
            { field: 'name', header: 'Customer' },
            { field: 'date', header: 'Date' },
            { field: 'status', header: 'Status' },
        ];
        this.prepareInvoices();
    }
    prepareInvoices(){
        this.invoices = [];
        for (let i = 0; i < 20; i++) {
            this.invoices.push({
                id: `INV-POS-${i + 1}`,
                name: 'Customer ' + (i + 1),
                date: new Date().toISOString(),
                status: Math.random() > 0.5 ? 'Paid' : 'Unpaid',
                item: this.addDefaultItemsToCart()
            });
        }
        this.cartItems = this.invoices[0].item;
    }
    addDefaultItemsToCart(){
        const allProdcuts = this.data.allProducts;
        const items: any[] = [];
        const itemsToAdd = Math.floor(Math.random() * 10) + 1;
        const availableProducts = [...allProdcuts];
        for (let i = 0; i < itemsToAdd; i++) {
            const randomIndex = Math.floor(Math.random() * availableProducts.length);
            const randomProduct = availableProducts[randomIndex];
            const quantity = Math.floor(Math.random() * 5) + 1;
            const checkExist = items.find(item => item.id === randomProduct.id);
            if(!checkExist){
                items.push({...randomProduct, quantity: quantity});
            }
            availableProducts.splice(randomIndex, 1);
            if (availableProducts.length === 0) {
                break;
            }
        }
        return items;
    }
    onSelect(event: any){
        this.cartItems = event?.event?.data?.item;
    }
}